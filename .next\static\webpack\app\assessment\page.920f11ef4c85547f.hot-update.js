"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx":
/*!*************************************************************!*\
  !*** ./src/components/assessment/MedicalHistorySection.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MedicalHistorySection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _SubstanceUseSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SubstanceUseSection */ \"(app-pages-browser)/./src/components/assessment/SubstanceUseSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MedicalHistorySection(param) {\n    let { data, onUpdate } = param;\n    var _formData_previousPsychiatricTreatment, _formData_traumaHistory;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data || {});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onUpdate(formData);\n    }, [\n        formData,\n        onUpdate\n    ]);\n    const handleBooleanChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value === \"true\"\n            }));\n    };\n    const handleStringChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubstanceUseUpdate = (substanceUseHistory)=>{\n        setFormData((prev)=>({\n                ...prev,\n                substanceUseHistory\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-2\",\n                        children: \"Medical History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600\",\n                        children: \"Provide comprehensive medical and psychiatric history information.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Current Medical Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.currentMedications || \"\",\n                                        onChange: (e)=>handleStringChange(\"currentMedications\", e.target.value),\n                                        placeholder: \"List all current medications, dosages, and frequency\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.allergies || \"\",\n                                        onChange: (e)=>handleStringChange(\"allergies\", e.target.value),\n                                        placeholder: \"List any known allergies to medications, foods, or other substances\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.medicalConditions || \"\",\n                                        onChange: (e)=>handleStringChange(\"medicalConditions\", e.target.value),\n                                        placeholder: \"List any current or past medical conditions\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Surgical History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.surgicalHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"surgicalHistory\", e.target.value),\n                                        placeholder: \"List any past surgeries and dates\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Psychiatric History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"Previous Psychiatric Treatment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_previousPsychiatricTreatment = formData.previousPsychiatricTreatment) === null || _formData_previousPsychiatricTreatment === void 0 ? void 0 : _formData_previousPsychiatricTreatment.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"previousPsychiatricTreatment\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"ppt-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"ppt-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"ppt-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"ppt-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Previous Hospitalizations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.previousHospitalizations || \"\",\n                                        onChange: (e)=>handleStringChange(\"previousHospitalizations\", e.target.value),\n                                        placeholder: \"Describe any previous psychiatric hospitalizations, dates, and reasons\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Previous Psychiatric Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.previousMedications || \"\",\n                                        onChange: (e)=>handleStringChange(\"previousMedications\", e.target.value),\n                                        placeholder: \"List previous psychiatric medications and their effectiveness\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Family Psychiatric History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.familyPsychiatricHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"familyPsychiatricHistory\", e.target.value),\n                                        placeholder: \"Describe any family history of mental illness, suicide, or substance abuse\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SubstanceUseSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                data: formData.substanceUseHistory || [],\n                onUpdate: handleSubstanceUseUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-lg\",\n                                children: \"Additional Substance Use Notes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Use the enhanced substance use section above for detailed entries. These fields are for additional notes or legacy data.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Alcohol Use Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.alcoholUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"alcoholUse\", e.target.value),\n                                        placeholder: \"Additional notes about alcohol use patterns\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Drug Use Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.drugUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"drugUse\", e.target.value),\n                                        placeholder: \"Additional notes about drug use\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Tobacco Use Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.tobaccoUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"tobaccoUse\", e.target.value),\n                                        placeholder: \"Additional notes about tobacco use\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Substance Abuse History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.substanceAbuseHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"substanceAbuseHistory\", e.target.value),\n                                        placeholder: \"Additional notes about substance abuse or dependence\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Trauma History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"History of Trauma\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_traumaHistory = formData.traumaHistory) === null || _formData_traumaHistory === void 0 ? void 0 : _formData_traumaHistory.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"traumaHistory\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"th-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"th-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"th-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"th-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            formData.traumaHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Trauma Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.traumaDetails || \"\",\n                                        onChange: (e)=>handleStringChange(\"traumaDetails\", e.target.value),\n                                        placeholder: \"Describe the nature of trauma experiences (be sensitive and general)\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicalHistorySection, \"JPolC2XS0g7tYnXkmRHL767uIww=\");\n_c = MedicalHistorySection;\nvar _c;\n$RefreshReg$(_c, \"MedicalHistorySection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx\n"));

/***/ })

});
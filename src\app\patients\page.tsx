"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Search, 
  Plus, 
  User, 
  Calendar, 
  FileText, 
  ArrowLeft,
  Edit,
  Eye,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react"
import Link from "next/link"

interface Assessment {
  id: string
  createdAt: string
  updatedAt: string
  assessorName: string
  status: string
  demographics?: {
    patientCode?: string
    age?: number
    gender?: string
  }
  _count?: {
    symptoms: number
    diagnoses: number
  }
}

export default function PatientsPage() {
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [filteredAssessments, setFilteredAssessments] = useState<Assessment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")

  useEffect(() => {
    fetchAssessments()
  }, [])

  useEffect(() => {
    // Filter assessments based on search term
    if (searchTerm.trim() === "") {
      setFilteredAssessments(assessments)
    } else {
      const filtered = assessments.filter(assessment => 
        assessment.demographics?.patientCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assessment.assessorName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assessment.id.toLowerCase().includes(searchTerm.toLowerCase())
      )
      setFilteredAssessments(filtered)
    }
  }, [searchTerm, assessments])

  const fetchAssessments = async () => {
    try {
      const response = await fetch('/api/assessments')
      if (response.ok) {
        const data = await response.json()
        setAssessments(data)
        setFilteredAssessments(data)
      }
    } catch (error) {
      console.error('Error fetching assessments:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800"><Clock className="h-3 w-3 mr-1" />In Progress</Badge>
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800"><FileText className="h-3 w-3 mr-1" />Draft</Badge>
      default:
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertCircle className="h-3 w-3 mr-1" />Unknown</Badge>
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="assessment-container py-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Link href="/">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-slate-900">Patient Management</h1>
            <p className="text-sm text-slate-600">Manage and continue patient assessments</p>
          </div>
        </div>
        
        <Link href="/assessment">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Assessment
          </Button>
        </Link>
      </div>

      {/* Search and Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Search Patients</CardTitle>
          <CardDescription>Find patients by anonymous code, assessor name, or assessment ID</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                placeholder="Search by patient code, assessor name, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-slate-600">
              {filteredAssessments.length} of {assessments.length} assessments
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Assessment List */}
      <Card>
        <CardHeader>
          <CardTitle>Assessment Records</CardTitle>
          <CardDescription>
            All patient assessments with status and completion information
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-slate-600">Loading assessments...</p>
            </div>
          ) : filteredAssessments.length === 0 ? (
            <div className="text-center py-8">
              <User className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600">
                {searchTerm ? 'No assessments match your search' : 'No assessments found'}
              </p>
              <p className="text-sm text-slate-500 mt-1">
                <Link href="/assessment" className="text-blue-600 hover:underline">
                  Create your first assessment
                </Link>
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAssessments.map((assessment) => (
                <div key={assessment.id} className="border rounded-lg p-4 hover:bg-slate-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <User className="h-4 w-4 text-slate-500" />
                        <span className="font-medium font-mono">
                          {assessment.demographics?.patientCode || 'No Patient Code'}
                        </span>
                        {getStatusBadge(assessment.status)}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-slate-600">
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>Created: {formatDate(assessment.createdAt)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>Updated: {formatDate(assessment.updatedAt)}</span>
                        </div>
                        <div>
                          <span>Age: {assessment.demographics?.age || 'N/A'}</span>
                        </div>
                        <div>
                          <span>Gender: {assessment.demographics?.gender || 'N/A'}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-4 mt-2 text-xs text-slate-500">
                        <span>Symptoms: {assessment._count?.symptoms || 0}</span>
                        <span>Diagnoses: {assessment._count?.diagnoses || 0}</span>
                        <span>Assessor: {assessment.assessorName}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Link href={`/assessment?id=${assessment.id}`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          {assessment.status === 'completed' ? 'View' : 'Continue'}
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx":
/*!*************************************************************!*\
  !*** ./src/components/assessment/MedicalHistorySection.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MedicalHistorySection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* harmony import */ var _SubstanceUseSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./SubstanceUseSection */ \"(app-pages-browser)/./src/components/assessment/SubstanceUseSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction MedicalHistorySection(param) {\n    let { data, onUpdate } = param;\n    var _formData_previousPsychiatricTreatment, _formData_traumaHistory;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data || {});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onUpdate(formData);\n    }, [\n        formData,\n        onUpdate\n    ]);\n    const handleBooleanChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value === \"true\"\n            }));\n    };\n    const handleStringChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    const handleSubstanceUseUpdate = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((substanceUseHistory)=>{\n        setFormData((prev)=>({\n                ...prev,\n                substanceUseHistory\n            }));\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-2\",\n                        children: \"Medical History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600\",\n                        children: \"Provide comprehensive medical and psychiatric history information.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Current Medical Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.currentMedications || \"\",\n                                        onChange: (e)=>handleStringChange(\"currentMedications\", e.target.value),\n                                        placeholder: \"List all current medications, dosages, and frequency\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.allergies || \"\",\n                                        onChange: (e)=>handleStringChange(\"allergies\", e.target.value),\n                                        placeholder: \"List any known allergies to medications, foods, or other substances\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.medicalConditions || \"\",\n                                        onChange: (e)=>handleStringChange(\"medicalConditions\", e.target.value),\n                                        placeholder: \"List any current or past medical conditions\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Surgical History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.surgicalHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"surgicalHistory\", e.target.value),\n                                        placeholder: \"List any past surgeries and dates\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Psychiatric History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"Previous Psychiatric Treatment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_previousPsychiatricTreatment = formData.previousPsychiatricTreatment) === null || _formData_previousPsychiatricTreatment === void 0 ? void 0 : _formData_previousPsychiatricTreatment.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"previousPsychiatricTreatment\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"ppt-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"ppt-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"ppt-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"ppt-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Previous Hospitalizations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.previousHospitalizations || \"\",\n                                        onChange: (e)=>handleStringChange(\"previousHospitalizations\", e.target.value),\n                                        placeholder: \"Describe any previous psychiatric hospitalizations, dates, and reasons\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Previous Psychiatric Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.previousMedications || \"\",\n                                        onChange: (e)=>handleStringChange(\"previousMedications\", e.target.value),\n                                        placeholder: \"List previous psychiatric medications and their effectiveness\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Family Psychiatric History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.familyPsychiatricHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"familyPsychiatricHistory\", e.target.value),\n                                        placeholder: \"Describe any family history of mental illness, suicide, or substance abuse\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SubstanceUseSection__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                data: formData.substanceUseHistory || [],\n                onUpdate: handleSubstanceUseUpdate\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-lg\",\n                                children: \"Additional Substance Use Notes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Use the enhanced substance use section above for detailed entries. These fields are for additional notes or legacy data.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Alcohol Use Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.alcoholUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"alcoholUse\", e.target.value),\n                                        placeholder: \"Additional notes about alcohol use patterns\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Drug Use Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.drugUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"drugUse\", e.target.value),\n                                        placeholder: \"Additional notes about drug use\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Tobacco Use Notes\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.tobaccoUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"tobaccoUse\", e.target.value),\n                                        placeholder: \"Additional notes about tobacco use\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"General Substance Abuse History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.substanceAbuseHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"substanceAbuseHistory\", e.target.value),\n                                        placeholder: \"Additional notes about substance abuse or dependence\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Trauma History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"History of Trauma\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_traumaHistory = formData.traumaHistory) === null || _formData_traumaHistory === void 0 ? void 0 : _formData_traumaHistory.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"traumaHistory\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"th-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"th-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"th-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"th-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this),\n                            formData.traumaHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Trauma Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.traumaDetails || \"\",\n                                        onChange: (e)=>handleStringChange(\"traumaDetails\", e.target.value),\n                                        placeholder: \"Describe the nature of trauma experiences (be sensitive and general)\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 267,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicalHistorySection, \"Z5X9uDL+HIf7wBFwKeIvHSxKmTQ=\");\n_c = MedicalHistorySection;\nvar _c;\n$RefreshReg$(_c, \"MedicalHistorySection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx\n"));

/***/ })

});
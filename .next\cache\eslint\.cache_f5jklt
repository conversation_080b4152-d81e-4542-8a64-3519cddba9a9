[{"C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\assessments\\route.ts": "1", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\export\\route.ts": "2", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\assessment\\page.tsx": "3", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\data\\page.tsx": "4", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\layout.tsx": "5", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\page.tsx": "6", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DemographicsSection.tsx": "7", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DiagnosisSection.tsx": "8", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MedicalHistorySection.tsx": "9", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MentalStatusExamSection.tsx": "10", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\RiskAssessmentSection.tsx": "11", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\SymptomsSection.tsx": "12", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\badge.tsx": "13", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\button.tsx": "14", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\card.tsx": "15", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\checkbox.tsx": "16", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\input.tsx": "17", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\label.tsx": "18", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\progress.tsx": "19", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\radio-group.tsx": "20", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\select.tsx": "21", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\tabs.tsx": "22", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\textarea.tsx": "23", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\constants.ts": "24", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\db.ts": "25", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\utils.ts": "26", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\DataExportUtility.tsx": "27", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\OptimizedDiagnosisSearch.tsx": "28", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\alert.tsx": "29", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\scroll-area.tsx": "30", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\separator.tsx": "31", "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\VirtualizedSymptomSelector.tsx": "32"}, {"size": 7097, "mtime": 1754682034880, "results": "33", "hashOfConfig": "34"}, {"size": 6037, "mtime": 1754647586583, "results": "35", "hashOfConfig": "34"}, {"size": 10963, "mtime": 1754512788606, "results": "36", "hashOfConfig": "34"}, {"size": 10964, "mtime": 1754427152912, "results": "37", "hashOfConfig": "34"}, {"size": 648, "mtime": 1754423739859, "results": "38", "hashOfConfig": "34"}, {"size": 12455, "mtime": 1754427199620, "results": "39", "hashOfConfig": "34"}, {"size": 13702, "mtime": 1754647522828, "results": "40", "hashOfConfig": "34"}, {"size": 10758, "mtime": 1754428063173, "results": "41", "hashOfConfig": "34"}, {"size": 8928, "mtime": 1754426043763, "results": "42", "hashOfConfig": "34"}, {"size": 17029, "mtime": 1754428077125, "results": "43", "hashOfConfig": "34"}, {"size": 13262, "mtime": 1754426002181, "results": "44", "hashOfConfig": "34"}, {"size": 8050, "mtime": 1754425936241, "results": "45", "hashOfConfig": "34"}, {"size": 1128, "mtime": 1754426910637, "results": "46", "hashOfConfig": "34"}, {"size": 1835, "mtime": 1754423284482, "results": "47", "hashOfConfig": "34"}, {"size": 1877, "mtime": 1754423327245, "results": "48", "hashOfConfig": "34"}, {"size": 1056, "mtime": 1754425731778, "results": "49", "hashOfConfig": "34"}, {"size": 824, "mtime": 1754423296828, "results": "50", "hashOfConfig": "34"}, {"size": 710, "mtime": 1754423480970, "results": "51", "hashOfConfig": "34"}, {"size": 777, "mtime": 1754425756984, "results": "52", "hashOfConfig": "34"}, {"size": 1467, "mtime": 1754425745873, "results": "53", "hashOfConfig": "34"}, {"size": 5615, "mtime": 1754423533159, "results": "54", "hashOfConfig": "34"}, {"size": 1883, "mtime": 1754423400668, "results": "55", "hashOfConfig": "34"}, {"size": 772, "mtime": 1754425719167, "results": "56", "hashOfConfig": "34"}, {"size": 2519, "mtime": 1754647544955, "results": "57", "hashOfConfig": "34"}, {"size": 300, "mtime": 1754423209607, "results": "58", "hashOfConfig": "34"}, {"size": 2222, "mtime": 1754423194540, "results": "59", "hashOfConfig": "34"}, {"size": 9398, "mtime": 1754511265890, "results": "60", "hashOfConfig": "34"}, {"size": 9743, "mtime": 1754511220011, "results": "61", "hashOfConfig": "34"}, {"size": 1584, "mtime": 1754511124158, "results": "62", "hashOfConfig": "34"}, {"size": 1640, "mtime": 1754511307921, "results": "63", "hashOfConfig": "34"}, {"size": 756, "mtime": 1754511322592, "results": "64", "hashOfConfig": "34"}, {"size": 7523, "mtime": 1754511175940, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wqmyzj", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\assessments\\route.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\api\\export\\route.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\assessment\\page.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\data\\page.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DemographicsSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\DiagnosisSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MedicalHistorySection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\MentalStatusExamSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\RiskAssessmentSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\assessment\\SymptomsSection.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\constants.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\db.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\DataExportUtility.tsx", ["162"], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\OptimizedDiagnosisSearch.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\projects\\psychiatric-assessment\\src\\components\\VirtualizedSymptomSelector.tsx", [], [], {"ruleId": "163", "severity": 1, "message": "164", "line": 55, "column": 6, "nodeType": "165", "endLine": 55, "endColumn": 22, "suggestions": "166"}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'exportData'. Either include it or remove the dependency array.", "ArrayExpression", ["167"], {"desc": "168", "fix": "169"}, "Update the dependencies array to be: [data, exportData, onExport]", {"range": "170", "text": "171"}, [1592, 1608], "[data, exportData, onExport]"]
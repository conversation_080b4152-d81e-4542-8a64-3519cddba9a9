"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/assessment/page",{

/***/ "(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx":
/*!*************************************************************!*\
  !*** ./src/components/assessment/MedicalHistorySection.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MedicalHistorySection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/radio-group */ \"(app-pages-browser)/./src/components/ui/radio-group.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction MedicalHistorySection(param) {\n    let { data, onUpdate } = param;\n    var _formData_previousPsychiatricTreatment, _formData_traumaHistory;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(data || {});\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        onUpdate(formData);\n    }, [\n        formData,\n        onUpdate\n    ]);\n    const handleBooleanChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value === \"true\"\n            }));\n    };\n    const handleStringChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-2\",\n                        children: \"Medical History\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-slate-600\",\n                        children: \"Provide comprehensive medical and psychiatric history information.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Current Medical Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Current Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.currentMedications || \"\",\n                                        onChange: (e)=>handleStringChange(\"currentMedications\", e.target.value),\n                                        placeholder: \"List all current medications, dosages, and frequency\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Allergies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.allergies || \"\",\n                                        onChange: (e)=>handleStringChange(\"allergies\", e.target.value),\n                                        placeholder: \"List any known allergies to medications, foods, or other substances\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Medical Conditions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.medicalConditions || \"\",\n                                        onChange: (e)=>handleStringChange(\"medicalConditions\", e.target.value),\n                                        placeholder: \"List any current or past medical conditions\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Surgical History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.surgicalHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"surgicalHistory\", e.target.value),\n                                        placeholder: \"List any past surgeries and dates\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Psychiatric History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"Previous Psychiatric Treatment\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_previousPsychiatricTreatment = formData.previousPsychiatricTreatment) === null || _formData_previousPsychiatricTreatment === void 0 ? void 0 : _formData_previousPsychiatricTreatment.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"previousPsychiatricTreatment\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"ppt-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"ppt-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"ppt-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"ppt-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Previous Hospitalizations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.previousHospitalizations || \"\",\n                                        onChange: (e)=>handleStringChange(\"previousHospitalizations\", e.target.value),\n                                        placeholder: \"Describe any previous psychiatric hospitalizations, dates, and reasons\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Previous Psychiatric Medications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.previousMedications || \"\",\n                                        onChange: (e)=>handleStringChange(\"previousMedications\", e.target.value),\n                                        placeholder: \"List previous psychiatric medications and their effectiveness\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Family Psychiatric History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.familyPsychiatricHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"familyPsychiatricHistory\", e.target.value),\n                                        placeholder: \"Describe any family history of mental illness, suicide, or substance abuse\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Substance Use History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Alcohol Use\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.alcoholUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"alcoholUse\", e.target.value),\n                                        placeholder: \"Describe current and past alcohol use patterns\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Drug Use\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.drugUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"drugUse\", e.target.value),\n                                        placeholder: \"Describe current and past drug use (prescription and illicit)\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Tobacco Use\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.tobaccoUse || \"\",\n                                        onChange: (e)=>handleStringChange(\"tobaccoUse\", e.target.value),\n                                        placeholder: \"Describe current and past tobacco use\",\n                                        rows: 2\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Substance Abuse History\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.substanceAbuseHistory || \"\",\n                                        onChange: (e)=>handleStringChange(\"substanceAbuseHistory\", e.target.value),\n                                        placeholder: \"Describe any history of substance abuse or dependence\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-lg\",\n                            children: \"Trauma History\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        className: \"text-base font-medium\",\n                                        children: \"History of Trauma\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroup, {\n                                        value: ((_formData_traumaHistory = formData.traumaHistory) === null || _formData_traumaHistory === void 0 ? void 0 : _formData_traumaHistory.toString()) || \"\",\n                                        onValueChange: (value)=>handleBooleanChange(\"traumaHistory\", value),\n                                        className: \"mt-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"false\",\n                                                        id: \"th-no\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 226,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"th-no\",\n                                                        children: \"No\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_radio_group__WEBPACK_IMPORTED_MODULE_5__.RadioGroupItem, {\n                                                        value: \"true\",\n                                                        id: \"th-yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                                        htmlFor: \"th-yes\",\n                                                        children: \"Yes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, this),\n                            formData.traumaHistory && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_3__.Label, {\n                                        children: \"Trauma Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                        value: formData.traumaDetails || \"\",\n                                        onChange: (e)=>handleStringChange(\"traumaDetails\", e.target.value),\n                                        placeholder: \"Describe the nature of trauma experiences (be sensitive and general)\",\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n                lineNumber: 213,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\projects\\\\psychiatric-assessment\\\\src\\\\components\\\\assessment\\\\MedicalHistorySection.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_s(MedicalHistorySection, \"JPolC2XS0g7tYnXkmRHL767uIww=\");\n_c = MedicalHistorySection;\nvar _c;\n$RefreshReg$(_c, \"MedicalHistorySection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/assessment/MedicalHistorySection.tsx\n"));

/***/ })

});
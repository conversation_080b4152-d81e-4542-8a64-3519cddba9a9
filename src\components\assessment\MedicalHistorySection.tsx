"use client"

import { useState, useEffect, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import SubstanceUseSection from "./SubstanceUseSection"

interface SubstanceUseEntry {
  id: string
  category: string
  specificSubstance: string
  duration: string
  route: string
  frequency: string
  ageOfFirstUse: string
  treatmentHistory: string
  notes: string
}

interface MedicalHistoryData {
  currentMedications?: string
  allergies?: string
  medicalConditions?: string
  surgicalHistory?: string
  previousPsychiatricTreatment?: boolean
  previousHospitalizations?: string
  previousMedications?: string
  familyPsychiatricHistory?: string
  alcoholUse?: string
  drugUse?: string
  tobaccoUse?: string
  substanceAbuseHistory?: string
  substanceUseHistory?: SubstanceUseEntry[]
  traumaHistory?: boolean
  traumaDetails?: string
}

interface MedicalHistorySectionProps {
  data: MedicalHistoryData
  onUpdate: (data: MedicalHistoryData) => void
}

export default function MedicalHistorySection({ data, onUpdate }: MedicalHistorySectionProps) {
  const [formData, setFormData] = useState<MedicalHistoryData>(data || {})

  useEffect(() => {
    onUpdate(formData)
  }, [formData, onUpdate])

  const handleBooleanChange = (field: keyof MedicalHistoryData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value === 'true'
    }))
  }

  const handleStringChange = (field: keyof MedicalHistoryData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubstanceUseUpdate = useCallback((substanceUseHistory: SubstanceUseEntry[]) => {
    setFormData(prev => ({
      ...prev,
      substanceUseHistory
    }))
  }, [])

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold mb-2">Medical History</h2>
        <p className="text-sm text-slate-600">Provide comprehensive medical and psychiatric history information.</p>
      </div>

      {/* Medical History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Current Medical Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Current Medications</Label>
            <Textarea
              value={formData.currentMedications || ''}
              onChange={(e) => handleStringChange('currentMedications', e.target.value)}
              placeholder="List all current medications, dosages, and frequency"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Allergies</Label>
            <Textarea
              value={formData.allergies || ''}
              onChange={(e) => handleStringChange('allergies', e.target.value)}
              placeholder="List any known allergies to medications, foods, or other substances"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label>Medical Conditions</Label>
            <Textarea
              value={formData.medicalConditions || ''}
              onChange={(e) => handleStringChange('medicalConditions', e.target.value)}
              placeholder="List any current or past medical conditions"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Surgical History</Label>
            <Textarea
              value={formData.surgicalHistory || ''}
              onChange={(e) => handleStringChange('surgicalHistory', e.target.value)}
              placeholder="List any past surgeries and dates"
              rows={2}
            />
          </div>
        </CardContent>
      </Card>

      {/* Psychiatric History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Psychiatric History</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-base font-medium">Previous Psychiatric Treatment</Label>
            <RadioGroup
              value={formData.previousPsychiatricTreatment?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('previousPsychiatricTreatment', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="ppt-no" />
                <Label htmlFor="ppt-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="ppt-yes" />
                <Label htmlFor="ppt-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label>Previous Hospitalizations</Label>
            <Textarea
              value={formData.previousHospitalizations || ''}
              onChange={(e) => handleStringChange('previousHospitalizations', e.target.value)}
              placeholder="Describe any previous psychiatric hospitalizations, dates, and reasons"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Previous Psychiatric Medications</Label>
            <Textarea
              value={formData.previousMedications || ''}
              onChange={(e) => handleStringChange('previousMedications', e.target.value)}
              placeholder="List previous psychiatric medications and their effectiveness"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label>Family Psychiatric History</Label>
            <Textarea
              value={formData.familyPsychiatricHistory || ''}
              onChange={(e) => handleStringChange('familyPsychiatricHistory', e.target.value)}
              placeholder="Describe any family history of mental illness, suicide, or substance abuse"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Substance Use History */}
      <SubstanceUseSection
        data={formData.substanceUseHistory || []}
        onUpdate={handleSubstanceUseUpdate}
      />

      {/* Legacy Substance Use Fields (for backward compatibility) */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Additional Substance Use Notes</CardTitle>
          <CardDescription>
            Use the enhanced substance use section above for detailed entries.
            These fields are for additional notes or legacy data.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>General Alcohol Use Notes</Label>
            <Textarea
              value={formData.alcoholUse || ''}
              onChange={(e) => handleStringChange('alcoholUse', e.target.value)}
              placeholder="Additional notes about alcohol use patterns"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label>General Drug Use Notes</Label>
            <Textarea
              value={formData.drugUse || ''}
              onChange={(e) => handleStringChange('drugUse', e.target.value)}
              placeholder="Additional notes about drug use"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label>General Tobacco Use Notes</Label>
            <Textarea
              value={formData.tobaccoUse || ''}
              onChange={(e) => handleStringChange('tobaccoUse', e.target.value)}
              placeholder="Additional notes about tobacco use"
              rows={2}
            />
          </div>

          <div className="space-y-2">
            <Label>General Substance Abuse History</Label>
            <Textarea
              value={formData.substanceAbuseHistory || ''}
              onChange={(e) => handleStringChange('substanceAbuseHistory', e.target.value)}
              placeholder="Additional notes about substance abuse or dependence"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Trauma History */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Trauma History</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-base font-medium">History of Trauma</Label>
            <RadioGroup
              value={formData.traumaHistory?.toString() || ''}
              onValueChange={(value) => handleBooleanChange('traumaHistory', value)}
              className="mt-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="false" id="th-no" />
                <Label htmlFor="th-no">No</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="true" id="th-yes" />
                <Label htmlFor="th-yes">Yes</Label>
              </div>
            </RadioGroup>
          </div>

          {formData.traumaHistory && (
            <div className="space-y-2">
              <Label>Trauma Details</Label>
              <Textarea
                value={formData.traumaDetails || ''}
                onChange={(e) => handleStringChange('traumaDetails', e.target.value)}
                placeholder="Describe the nature of trauma experiences (be sensitive and general)"
                rows={3}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
